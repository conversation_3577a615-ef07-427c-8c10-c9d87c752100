<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel ADRIA München</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Alta Regular", Sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
            background-color: #f5f2eb;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        /* Header */
        header {
            background: white;
            padding: 15px 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .menu-icon {
            font-size: 24px;
            cursor: pointer;
            transition: transform 0.3s ease;
            color: #2e86c1;
            background: none;
            border: none;
            padding: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .menu-icon:hover {
            transform: scale(1.1);
            background: rgba(46, 134, 193, 0.1);
        }
        /* Modern Sidebar Menu */
        .sidebar {
            position: fixed;
            top: 0;
            left: -450px;
            width: 450px;
            height: 100vh;
            background: white;
            z-index: 2000;
            transition: left 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow: 10px 0 30px rgba(0,0,0,0.3);
            overflow-y: auto;
        }
        .sidebar.open {
            left: 0;
        }
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0,0,0,0.6);
            z-index: 1500;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s ease;
            backdrop-filter: blur(5px);
        }
        .sidebar-overlay.open {
            opacity: 1;
            visibility: visible;
        }
        .sidebar-header {
            padding: 40px 30px 30px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            background: white;
        }
        .sidebar-logo {
        width: 111px;
            height: 70px;
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        .sidebar-logo-img {
            width: 100px;
            height: 60px;
            background: url('LOGO.jpg') center/cover;
            border-radius: 12px;
            border: 2px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
            object-fit: cover;
            flex-shrink: 0;
        }
        .sidebar-logo-img:hover {
            border-color: #3498db;
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.4);
        }
        .sidebar-logo-text {
            color: #333;
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 1px;
            text-transform: uppercase;
        }
        .close-sidebar {
            position: absolute;
            top: 25px;
            right: 25px;
            font-size: 28px;
            cursor: pointer;
            color: #333;
            background: rgba(0,0,0,0.1);
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .close-sidebar:hover {
            background: rgba(0,0,0,0.2);
            transform: rotate(90deg);
        }
        .sidebar-nav {
            padding: 30px 0;
        }
        .sidebar-nav ul {
            list-style: none;
        }
        .sidebar-nav li {
            margin-bottom: 5px;
        }
        .sidebar-nav a {
            text-decoration: none;
            color: #333;
            font-size: 18px;
            font-weight: 400;
            letter-spacing: 1px;
            text-transform: uppercase;
            transition: all 0.3s ease;
            display: block;
            padding: 15px 30px;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }
        .sidebar-nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
            transition: left 0.5s ease;
        }
        .sidebar-nav a:hover {
            color: #2e86c1;
            border-left-color: #2e86c1;
            background: rgba(46, 134, 193, 0.1);
            transform: translateX(10px);
        }
        .sidebar-nav a:hover::before {
            left: 100%;
        }
        .sidebar-nav .dropdown {
            position: relative;
        }
        .sidebar-nav .dropdown-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .sidebar-nav .dropdown-toggle::after {
            content: '▼';
            font-size: 12px;
            transition: transform 0.3s ease;
            margin-right: 10px;
        }
        .sidebar-nav .dropdown.open .dropdown-toggle::after {
            transform: rotate(180deg);
        }
        .sidebar-nav .dropdown-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease;
            background: rgba(0,0,0,0.05);
        }
        .sidebar-nav .dropdown.open .dropdown-menu {
            max-height: 300px;
        }
        .sidebar-nav .dropdown-menu a {
            font-size: 16px;
            padding: 12px 30px 12px 60px;
            color: #666;
            border-left: 3px solid transparent;
        }
        .sidebar-nav .dropdown-menu a:hover {
            color: #2e86c1;
            border-left-color: #2e86c1;
            background: rgba(46, 134, 193, 0.05);
        }
        .language-switcher {
            position: absolute;
            bottom: 30px;
            left: 30px;
            right: 30px;
            padding: 20px 0;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        .language-switcher h4 {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .language-flags {
            display: flex;
            gap: 15px;
        }
        .language-flag {
            width: 50px;
            height: 35px;
            cursor: pointer;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }
        .language-flag:hover {
            border-color: #3498db;
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        .language-flag.active {
            border-color: #3498db;
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.5);
        }
        .flag-en {
            background: linear-gradient(to bottom,
                #012169 0%, #012169 33%,
                white 33%, white 66%,
                #C8102E 66%, #C8102E 100%);
            position: relative;
        }
        .flag-en::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(45deg, transparent 40%, white 40%, white 45%, transparent 45%),
               linear-gradient(-45deg, transparent 40%, white 40%, white 45%, transparent 45%),
               linear-gradient(45deg, transparent 47%, #C8102E 47%, #C8102E 53%, transparent 53%),
               linear-gradient(-45deg, transparent 47%, #C8102E 47%, #C8102E 53%, transparent 53%);
        }
        .flag-de {
            background: linear-gradient(to bottom,
                #000000 0%, #000000 33%,
                #DD0000 33%, #DD0000 66%,
                #FFCE00 66%, #FFCE00 100%);
        }
        .logo {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .logo-img {
            width: 70px;
            height: 45px;
            background: url('LOGO.jpg') center/cover;
            border-radius: 8px;
            border: 2px solid #2e86c1;
            box-shadow: 0 3px 12px rgba(46, 134, 193, 0.2);
            transition: all 0.3s ease;
            object-fit: cover;
        }
        .logo-img:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(46, 134, 193, 0.3);
        }
        .logo-text {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            letter-spacing: 1.2px;
            text-transform: uppercase;
        }
        .logo-subtitle {
            font-size: 11px;
            color: #666;
            letter-spacing: 1.2px;
            margin-top: 2px;
            text-transform: uppercase;
        }
        .header-actions {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        .contact-icons {
            display: flex;
            gap: 15px;
        }
        .contact-icon {
            color: #2e86c1;
            font-size: 20px;
            text-decoration: none;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        .contact-icon:hover {
            background: rgba(46, 134, 193, 0.1);
            transform: scale(1.1);
        }
        .book-btn {
            background: transparent;
            border: 2px solid #2e86c1;
            color: #2e86c1;
            padding: 12px 24px;
            text-decoration: none;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 14px;
            font-family: "Alta Regular", Sans-serif;
        }
        .book-btn:hover {
            background: #2e86c1;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(46, 134, 193, 0.3);
        }
        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                url('adria-backround.webp');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }
        .hero-content {
            max-width: 800px;
            z-index: 2;
        }
        .hero h1 {
            font-size: 4rem;
            font-weight: 300;
            margin-bottom: 20px;
            letter-spacing: 3px;
            text-transform: uppercase;
        }
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
        .hero-tagline {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 40px;
            letter-spacing: 2px;
        }
        .hero-book-btn {
            background: transparent;
            border: 2px solid white;
            color: white;
            padding: 15px 40px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border-radius: 6px;
            font-family: "Alta Regular", Sans-serif;
        }
        .hero-book-btn:hover {
            background: #2e86c1;
            border-color: #2e86c1;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 134, 193, 0.4);
        }
        /* Room Booking Section */
        .booking-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            padding: 80px 0;
            color: white;
        }
        .booking-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 60px;
            font-weight: 300;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
        .booking-form {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        .form-group label {
            margin-bottom: 8px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 14px;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 12px 15px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255,255,255,0.7);
        }
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            background: rgba(255,255,255,0.15);
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.3);
        }
        .form-group select option {
            background: #2c3e50;
            color: white;
        }
        .room-categories {
            margin-bottom: 30px;
        }
        .room-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
        }
        .room-option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .room-option:hover {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.2);
            transform: translateY(-5px);
        }
        .room-option.selected {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.3);
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.4);
        }
        .room-option h4 {
            margin-bottom: 10px;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .room-option p {
            font-size: 14px;
            opacity: 0.8;
        }
        .book-now-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            color: white;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 2px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 5px 20px rgba(52, 152, 219, 0.3);
        }
        .book-now-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.5);
        }
        /* Image Gallery Section */
        .gallery-section {
            padding: 100px 0;
            background: #f5f2eb;
        }
        .gallery-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 60px;
            color: #333;
            font-weight: 300;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 60px;
        }
        .gallery-item {
            aspect-ratio: 1;
            background-size: cover;
            background-position: center;
            border-radius: 15px;
            overflow: hidden;
            transition: transform 0.3s ease;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .gallery-item:nth-child(1) { background-image: url('m1-adria.png'); }
        .gallery-item:nth-child(2) { background-image: url('m2-adria.png'); }
        .gallery-item:nth-child(3) { background-image: url('m7-adria.png'); }
        .gallery-item:nth-child(4) { background-image: url('m6-adria.png'); }
        .gallery-item:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        .gallery-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #2e86c1, #f39c12, #e74c3c, #8e44ad);
            opacity: 0.1;
        }
        /* Lifestyle Section */
        .lifestyle-section {
            background: #f5f2eb;
            padding: 100px 0;
        }
        .lifestyle-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 80px;
            color: #333;
            font-weight: 300;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
        .lifestyle-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-bottom: 80px;
        }
        .lifestyle-item {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .lifestyle-item:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        .lifestyle-image {
            height: 250px;
            background-size: cover;
            background-position: center;
            position: relative;
        }
        .lifestyle-item:nth-child(1) .lifestyle-image { background-image: url('FAM_2 (1).jpg'); }
        .lifestyle-item:nth-child(2) .lifestyle-image { background-image: url('FAM_4.jpg'); }
        .lifestyle-item:nth-child(3) .lifestyle-image { background-image: url('Comfort_1.jpg'); }
        .lifestyle-item:nth-child(4) .lifestyle-image { background-image: url('Deluxe_1.jpg'); }
        /* Quote Section */
        .quote-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        .quote-box {
            background: rgba(255,255,255,0.1);
            padding: 60px 40px;
            border: 3px solid rgba(52, 152, 219, 0.5);
            max-width: 700px;
            margin: 0 auto;
            backdrop-filter: blur(20px);
            border-radius: 20px;
        }
        .quote-title {
            font-size: 2.5rem;
            margin-bottom: 30px;
            font-weight: 300;
            letter-spacing: 3px;
            text-transform: uppercase;
        }
        .quote-subtitle {
            font-size: 1.8rem;
            margin-bottom: 40px;
            font-weight: 300;
            letter-spacing: 2px;
            text-transform: uppercase;
            color: #3498db;
        }
        .quote-text {
            font-size: 1.2rem;
            font-style: italic;
            letter-spacing: 1px;
            line-height: 1.8;
        }
        /* Lounge Section */
        .lounge-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 80vh;
        }
        .lounge-images {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            padding: 40px;
            background: #1a1a1a;
        }
        .lounge-image {
            aspect-ratio: 1;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            background-size: cover;
            background-position: center;
            transition: transform 0.3s ease;
        }
        .lounge-image:hover {
            transform: scale(1.05);
        }
        .lounge-image:nth-child(1) { background-image: url('Comfort_1.jpg'); }
        .lounge-image:nth-child(2) { background-image: url('Comfort_5.jpg'); }
        .lounge-image:nth-child(3) { background-image: url('Deluxe_1.jpg'); }
        .lounge-image:nth-child(4) { background-image: url('Deluxe_2.jpg'); }
        .lounge-content {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 80px 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .lounge-title {
            font-size: 3rem;
            margin-bottom: 40px;
            font-weight: 300;
            letter-spacing: 3px;
            line-height: 1.2;
        }
        .lounge-description {
            font-size: 1.2rem;
            line-height: 1.8;
            opacity: 0.9;
        }
        /* Contact Section - New Green Design */
        .contact-section {
            background: #1a4a3a;
            padding: 100px 0;
            color: white;
        }
        .contact-main-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 40px;
            color: white;
            font-weight: 300;
            letter-spacing: 4px;
            text-transform: uppercase;
        }
        .contact-divider {
            width: 300px;
            height: 1px;
            background: rgba(255,255,255,0.5);
            margin: 0 auto 60px;
        }
        .contact-form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .contact-form {
            display: grid;
            gap: 30px;
        }
        .contact-form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .contact-form-group {
            display: flex;
            flex-direction: column;
        }
        .contact-form-group.full-width {
            grid-column: 1 / -1;
        }
        .contact-form label {
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 400;
            color: white;
            letter-spacing: 1px;
        }
        .contact-form label .required {
            color: #ff6b6b;
            margin-left: 2px;
        }
        .contact-form input,
        .contact-form textarea {
            background: transparent;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 0;
            padding: 15px 20px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        .contact-form input::placeholder,
        .contact-form textarea::placeholder {
            color: rgba(255,255,255,0.5);
        }
        .contact-form input:focus,
        .contact-form textarea:focus {
            outline: none;
            border-color: rgba(255,255,255,0.6);
            background: rgba(255,255,255,0.05);
        }
        .contact-form textarea {
            resize: vertical;
            min-height: 120px;
        }
        .contact-submit-btn {
            background: transparent;
            border: 1px solid rgba(255,255,255,0.5);
            color: white;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 2px;
            border-radius: 0;
            cursor: pointer;
            transition: all 0.3s ease;
            justify-self: center;
            margin-top: 20px;
            font-family: inherit;
        }
        .contact-submit-btn:hover {
            background: rgba(255,255,255,0.1);
            border-color: white;
            transform: translateY(-2px);
        }
        /* Language Content */
        .lang-content {
            display: none;
        }
        .lang-content.active {
            display: block;
        }
        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 100vw;
                left: -100vw;
            }
            
            .logo-text {
                font-size: 14px;
            }
            
            .logo-subtitle {
                font-size: 10px;
            }
            
            .logo-img {
                width: 60px;
                height: 38px;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero-tagline {
                font-size: 1.8rem;
            }
            
            .image-grid,
            .lifestyle-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .lounge-section {
                grid-template-columns: 1fr;
            }
            
            .lounge-images {
                order: 2;
            }
            
            .header-content {
                padding: 0 15px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            .room-grid {
                grid-template-columns: 1fr;
            }
            .contact-form-row {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .contact-main-title {
                font-size: 2rem;
            }
        }
        @media (max-width: 480px) {
            .image-grid,
            .lifestyle-grid {
                grid-template-columns: 1fr;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .hero-tagline {
                font-size: 1.5rem;
            }
            
            .quote-title {
                font-size: 2rem;
            }
            
            .lounge-title {
                font-size: 2rem;
            }
            .booking-form {
                padding: 20px;
                margin: 0 20px;
            }
            .contact-main-title {
                font-size: 1.8rem;
            }
            .contact-form-container {
                padding: 0 15px;
            }
        }
        /* Animation effects */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .hero-content > * {
            animation: fadeInUp 1s ease-out;
        }
        .hero-content > *:nth-child(2) { animation-delay: 0.2s; }
        .hero-content > *:nth-child(3) { animation-delay: 0.4s; }
        .hero-content > *:nth-child(4) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-content">
            <button class="menu-icon" onclick="toggleSidebar()">☰</button>
            
            <div class="logo">
                <div class="logo-img"></div>
               
            </div>
            
            <div class="header-actions">
                <div class="contact-icons">
                    <a href="mailto:<EMAIL>" class="contact-icon">✉</a>
                    <a href="tel:+498938887780" class="contact-icon">☎</a>
                </div>
                <a href="#booking" class="book-btn">Book Now</a>
            </div>
        </div>
    </header>

    <!-- Modern Sidebar Menu -->
    <div class="sidebar-overlay" onclick="closeSidebar()"></div>
    <div class="sidebar">
        <div class="sidebar-header">
            <button class="close-sidebar" onclick="closeSidebar()">×</button>
            <div class="sidebar-logo">
                <div class="sidebar-logo-img"></div>
             
            </div>
        </div>
        
       <nav class="sidebar-nav">
            <ul>
                <li><a href="#fact-sheet">Fact Sheet</a></li>
                <li><a href="#hotel-rooms">Hotel Rooms & Prices</a></li>
                <li><a href="#offers">Offers</a></li>
                <li><a href="#culture">Culture & Culinary</a></li>
                <li><a href="#events">Trade Fairs & Events</a></li>
                <li><a href="#location">Location & Directions</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
        
        <div class="language-switcher">
            <div class="language-flags">
                <div class="language-flag flag-en active" onclick="switchLanguage('en')" title="English"></div>
                <div class="language-flag flag-de" onclick="switchLanguage('de')" title="Deutsch"></div>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="lang-content active" data-lang="en">
                <h1>HOTEL ADRIA</h1>
                <div class="hero-subtitle">München</div>
                <div class="hero-tagline">Mediterranean Atmosphere</div>
                <a href="#booking" class="hero-book-btn">Book</a>
            </div>
            <div class="lang-content" data-lang="de">
                
                <div class="hero-tagline">Mediterrane Atmosphäre</div>
                <a href="#booking" class="hero-book-btn">Jetzt Buchen</a>
            </div>
        </div>
    </section>

    <!-- Room Booking Section -->
    <section id="booking" class="booking-section">
        <div class="container">
            <div class="lang-content active" data-lang="en">
                <h2 class="booking-title">Book Your Perfect Stay</h2>
                <div class="booking-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="checkin">Check-in Date</label>
                            <input type="date" id="checkin" name="checkin" required>
                        </div>
                        <div class="form-group">
                            <label for="checkout">Check-out Date</label>
                            <input type="date" id="checkout" name="checkout" required>
                        </div>
                        <div class="form-group">
                            <label for="guests">Guests</label>
                            <select id="guests" name="guests" required>
                                <option value="">Select Guests</option>
                                <option value="1">1 Guest</option>
                                <option value="2">2 Guests</option>
                                <option value="3">3 Guests</option>
                                <option value="4">4 Guests</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="room-categories">
                        <h3 style="margin-bottom: 20px; text-align: center; font-size: 1.5rem; letter-spacing: 1px;">Select Room Category</h3>
                        <div class="room-grid">
                            <div class="room-option" onclick="selectRoom(this, 'classic')" data-room="classic">
                                <h4>Classic Double Room</h4>
                                <p>Comfortable accommodation with essential amenities</p>
                            </div>
                            <div class="room-option" onclick="selectRoom(this, 'comfort')" data-room="comfort">
                                <h4>Comfort Double Room</h4>
                                <p>Enhanced comfort with additional space and amenities</p>
                            </div>
                            <div class="room-option" onclick="selectRoom(this, 'junior')" data-room="junior">
                                <h4>Junior Suite</h4>
                                <p>Spacious suite with separate living area</p>
                            </div>
                            <div class="room-option" onclick="selectRoom(this, 'deluxe')" data-room="deluxe">
                                <h4>Deluxe Studio</h4>
                                <p>Premium studio with luxury amenities</p>
                            </div>
                        </div>
                    </div>
                    
                    <button class="book-now-btn" onclick="processBooking()">Book Now</button>
                </div>
            </div>
            
            <div class="lang-content" data-lang="de">
                <h2 class="booking-title">Buchen Sie Ihren Perfekten Aufenthalt</h2>
                <div class="booking-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="checkin-de">Anreisedatum</label>
                            <input type="date" id="checkin-de" name="checkin" required>
                        </div>
                        <div class="form-group">
                            <label for="checkout-de">Abreisedatum</label>
                            <input type="date" id="checkout-de" name="checkout" required>
                        </div>
                        <div class="form-group">
                            <label for="guests-de">Gäste</label>
                            <select id="guests-de" name="guests" required>
                                <option value="">Gäste Auswählen</option>
                                <option value="1">1 Gast</option>
                                <option value="2">2 Gäste</option>
                                <option value="3">3 Gäste</option>
                                <option value="4">4 Gäste</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="room-categories">
                        <h3 style="margin-bottom: 20px; text-align: center; font-size: 1.5rem; letter-spacing: 1px;">Zimmerkategorie Wählen</h3>
                        <div class="room-grid">
                            <div class="room-option" onclick="selectRoom(this, 'classic')" data-room="classic">
                                <h4>Classic Doppelzimmer</h4>
                                <p>Komfortable Unterkunft mit wesentlichen Annehmlichkeiten</p>
                            </div>
                            <div class="room-option" onclick="selectRoom(this, 'comfort')" data-room="comfort">
                                <h4>Komfort Doppelzimmer</h4>
                                <p>Erhöhter Komfort mit zusätzlichem Platz</p>
                            </div>
                            <div class="room-option" onclick="selectRoom(this, 'junior')" data-room="junior">
                                <h4>Junior Suite</h4>
                                <p>Geräumige Suite mit separatem Wohnbereich</p>
                            </div>
                            <div class="room-option" onclick="selectRoom(this, 'deluxe')" data-room="deluxe">
                                <h4>Deluxe Studio</h4>
                                <p>Premium Studio mit Luxusausstattung</p>
                            </div>
                        </div>
                    </div>
                    
                    <button class="book-now-btn" onclick="processBooking()">Jetzt Buchen</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery-section">
        <div class="container">
            <div class="lang-content active" data-lang="en">
                <h2 class="gallery-title">Three Star Superior Hotel in Munich</h2>
            </div>
            <div class="lang-content" data-lang="de">
                <h2 class="gallery-title">Drei Sterne Superior Hotel in München</h2>
            </div>
            <div class="image-grid">
                <div class="gallery-item"></div>
                <div class="gallery-item"></div>
                <div class="gallery-item"></div>
                <div class="gallery-item"></div>
            </div>
        </div>
    </section>

    <!-- Lifestyle Grid -->
    <section class="lifestyle-section">
        <div class="container">
            <div class="lang-content active" data-lang="en">
                <h2 class="lifestyle-title">Modern Hotel Rooms & Amenities</h2>
            </div>
            <div class="lang-content" data-lang="de">
                <h2 class="lifestyle-title">Moderne Hotelzimmer & Annehmlichkeiten</h2>
            </div>
            <div class="lifestyle-grid">
                <div class="lifestyle-item">
                    <div class="lifestyle-image"></div>
                </div>
                <div class="lifestyle-item">
                    <div class="lifestyle-image"></div>
                </div>
                <div class="lifestyle-item">
                    <div class="lifestyle-image"></div>
                </div>
                <div class="lifestyle-item">
                    <div class="lifestyle-image"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quote Section -->
    <section class="quote-section">
        <div class="container">
            <div class="quote-box">
                <div class="lang-content active" data-lang="en">
                    <h2 class="quote-title">Peaceful Accommodation</h2>
                    <h3 class="quote-subtitle">Next to English Garden</h3>
                    <p class="quote-text">"There is no better pleasure than to surprise a person in giving him more than expected!" - Charles Baudelaire</p>
                </div>
                <div class="lang-content" data-lang="de">
                    <h2 class="quote-title">Ruhige Unterkunft</h2>
                    <h3 class="quote-subtitle">Neben dem Englischen Garten</h3>
                    <p class="quote-text">"Es gibt kein größeres Vergnügen, als eine Person zu überraschen, indem man ihr mehr gibt, als sie erwartet!" - Charles Baudelaire</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Lounge Section -->
    <section class="lounge-section">
        <div class="lounge-images">
            <div class="lounge-image"></div>
            <div class="lounge-image"></div>
            <div class="lounge-image"></div>
            <div class="lounge-image"></div>
        </div>
        <div class="lounge-content">
            <div class="lang-content active" data-lang="en">
                <h2 class="lounge-title">Your Gateway to Munich's Heart</h2>
                <p class="lounge-description">
                    Hotel ADRIA München offers a peaceful accommodation in a quiet residential street, close to the English Garden with its gorgeous beer gardens. Our three star superior hotel provides Mediterranean atmosphere with 44 well-appointed guest rooms equipped with the latest communication technologies including wireless LAN internet connection, direct-dial telephone, and LCD satellite TV with 6 free SKY channels.
                    <br><br>
                    Located just steps from the Isar river, cultural sights such as the Munich State Opera, the residence, and the Haus der Kunst can easily be reached within minutes. The Odeonsplatz, Hofgarten, pedestrian area, and Theatiner street are conveniently accessible on foot. Munich - whether you see it as Bavaria's metropolis or Italy's northernmost city - Hotel ADRIA is your ideal starting point for all activities.
                </p>
            </div>
            <div class="lang-content" data-lang="de">
                <h2 class="lounge-title">Ihr Tor zu Münchens Herz</h2>
                <p class="lounge-description">
                    Hotel ADRIA München bietet eine ruhige Unterkunft in einer ruhigen Wohnstraße, in der Nähe des Englischen Gartens mit seinen wunderschönen Biergärten. Unser Drei-Sterne-Superior-Hotel bietet mediterrane Atmosphäre mit 44 gut ausgestatteten Gästezimmern mit modernster Kommunikationstechnik, WLAN-Internetverbindung, Direktwahltelefon und LCD-Satelliten-TV mit 6 kostenlosen SKY-Kanälen.
                    <br><br>
                    Nur wenige Schritte von der Isar entfernt, sind kulturelle Sehenswürdigkeiten wie die Münchner Staatsoper, die Residenz und das Haus der Kunst in wenigen Minuten erreichbar. Der Odeonsplatz, Hofgarten, die Fußgängerzone und die Theatinerstraße sind bequem zu Fuß erreichbar. München - ob Sie es als Bayerns Metropole oder als Italiens nördlichste Stadt sehen - Hotel ADRIA ist Ihr idealer Ausgangspunkt für alle Aktivitäten.
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Section - New Green Design -->
    <section id="contact" class="contact-section">
        <div class="lang-content active" data-lang="en">
            <h2 class="contact-main-title">STAY IN TOUCH</h2>
            <div class="contact-divider"></div>
            
            <div class="contact-form-container">
                <form class="contact-form" onsubmit="submitContactForm(event)">
                    <div class="contact-form-row">
                        <div class="contact-form-group">
                            <label for="firstname">First Name <span class="required">*</span></label>
                            <input type="text" id="firstname" name="firstname" required>
                        </div>
                        <div class="contact-form-group">
                            <label for="lastname">Last Name <span class="required">*</span></label>
                            <input type="text" id="lastname" name="lastname" required>
                        </div>
                    </div>
                    
                    <div class="contact-form-row">
                        <div class="contact-form-group">
                            <label for="email-address">Email Address <span class="required">*</span></label>
                            <input type="email" id="email-address" name="email" required>
                        </div>
                        <div class="contact-form-group">
                            <label for="telephone">Telephone <span class="required">*</span></label>
                            <input type="tel" id="telephone" name="telephone" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="contact-submit-btn">SUBMIT</button>
                </form>
            </div>
        </div>
        
        <div class="lang-content" data-lang="de">
            <h2 class="contact-main-title">BLEIBEN SIE IN KONTAKT</h2>
            <div class="contact-divider"></div>
            
            <div class="contact-form-container">
                <form class="contact-form" onsubmit="submitContactForm(event)">
                    <div class="contact-form-row">
                        <div class="contact-form-group">
                            <label for="firstname-de">Vorname <span class="required">*</span></label>
                            <input type="text" id="firstname-de" name="firstname" required>
                        </div>
                        <div class="contact-form-group">
                            <label for="lastname-de">Nachname <span class="required">*</span></label>
                            <input type="text" id="lastname-de" name="lastname" required>
                        </div>
                    </div>
                    
                    <div class="contact-form-row">
                        <div class="contact-form-group">
                            <label for="email-address-de">E-Mail-Adresse <span class="required">*</span></label>
                            <input type="email" id="email-address-de" name="email" required>
                        </div>
                        <div class="contact-form-group">
                            <label for="telephone-de">Telefon <span class="required">*</span></label>
                            <input type="tel" id="telephone-de" name="telephone" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="contact-submit-btn">SENDEN</button>
                </form>
            </div>
        </div>
    </section>

    <script>
        let currentLang = 'en';
        let selectedRoom = null;

        // Sidebar functionality
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            
            sidebar.classList.toggle('open');
            overlay.classList.toggle('open');
        }

        function closeSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            
            sidebar.classList.remove('open');
            overlay.classList.remove('open');
        }

        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = event.target.closest('.dropdown');
            dropdown.classList.toggle('open');
        }

        // Language switching functionality
        function switchLanguage(lang) {
            if (lang === currentLang) return;
            
            currentLang = lang;
            
            // Update language flag active state
            document.querySelectorAll('.language-flag').forEach(flag => {
                flag.classList.remove('active');
            });
            document.querySelector(`.flag-${lang}`).classList.add('active');
            
            // Show/hide language content
            document.querySelectorAll('.lang-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll(`[data-lang="${lang}"]`).forEach(content => {
                content.classList.add('active');
            });
            
            // Close sidebar after language change
            closeSidebar();
        }

        // Room selection functionality
        function selectRoom(element, roomType) {
            // Remove previous selection
            document.querySelectorAll('.room-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // Add selection to clicked room
            element.classList.add('selected');
            selectedRoom = roomType;
        }

        // Booking process
        function processBooking() {
            const checkinInput = currentLang === 'en' ? 
                document.getElementById('checkin') : 
                document.getElementById('checkin-de');
            const checkoutInput = currentLang === 'en' ? 
                document.getElementById('checkout') : 
                document.getElementById('checkout-de');
            const guestsInput = currentLang === 'en' ? 
                document.getElementById('guests') : 
                document.getElementById('guests-de');
            
            const checkin = checkinInput.value;
            const checkout = checkoutInput.value;
            const guests = guestsInput.value;
            
            if (!checkin || !checkout || !guests || !selectedRoom) {
                const message = currentLang === 'en' ? 
                    'Please fill in all fields and select a room category.' :
                   'Bitte füllen Sie alle Felder aus und wählen Sie eine Zimmerkategorie.';
                alert(message);
                return;
            }
            
            // Create booking summary
            const roomNames = {
                en: {
                    classic: 'Classic Double Room',
                    comfort: 'Comfort Double Room',
                    junior: 'Junior Suite',
                    deluxe: 'Deluxe Studio'
                },
                de: {
                    classic: 'Classic Doppelzimmer',
                    comfort: 'Komfort Doppelzimmer',
                    junior: 'Junior Suite',
                    deluxe: 'Deluxe Studio'
                }
            };
            
            const summary = currentLang === 'en' ? 
                `Booking Summary:\n\nCheck-in: ${checkin}\nCheck-out: ${checkout}\nGuests: ${guests}\nRoom: ${roomNames.en[selectedRoom]}\n\nThank you for choosing Hotel ADRIA München!` :
               `Buchungsübersicht:\n\nAnreise: ${checkin}\nAbreise: ${checkout}\nGäste: ${guests}\nZimmer: ${roomNames.de[selectedRoom]}\n\nVielen Dank, dass Sie sich für Hotel ADRIA München entschieden haben!`;
            
            alert(summary);
        }

        // Contact form submission
        function submitContactForm(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const firstname = formData.get('firstname');
            const lastname = formData.get('lastname');
            const email = formData.get('email');
            const telephone = formData.get('telephone');
            
            if (!firstname || !lastname || !email || !telephone) {
                const alertMessage = currentLang === 'en' ?
                    'Please fill in all required fields.' :
                    'Bitte füllen Sie alle Pflichtfelder aus.';
                alert(alertMessage);
                return;
            }
            
            const successMessage = currentLang === 'en' ?
                `Thank you for your message!\n\nFirst Name: ${firstname}\nLast Name: ${lastname}\nEmail: ${email}\nTelephone: ${telephone}\n\nWe will get back to you soon!` :
                `Vielen Dank für Ihre Nachricht!\n\nVorname: ${firstname}\nNachname: ${lastname}\nE-Mail: ${email}\nTelefon: ${telephone}\n\nWir werden uns bald bei Ihnen melden!`;
            
            alert(successMessage);
            event.target.reset();
        }

        // Close sidebar when clicking on overlay
        document.querySelector('.sidebar-overlay').addEventListener('click', closeSidebar);

        // Close sidebar on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSidebar();
            }
        });

        // Simple smooth scrolling for book buttons
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
                // Close sidebar after navigation
                closeSidebar();
            });
        });

        // Header background on scroll
        window.addEventListener('scroll', function() {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.backdropFilter = 'blur(10px)';
            } else {
                header.style.background = 'white';
                header.style.backdropFilter = 'none';
            }
        });

        // Set minimum date for booking inputs
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('checkin').min = today;
            document.getElementById('checkout').min = today;
            document.getElementById('checkin-de').min = today;
            document.getElementById('checkout-de').min = today;
            
            // Update checkout minimum when checkin changes
            document.getElementById('checkin').addEventListener('change', function() {
                document.getElementById('checkout').min = this.value;
            });
            document.getElementById('checkin-de').addEventListener('change', function() {
                document.getElementById('checkout-de').min = this.value;
            });
        });
    </script>
</body>
</html>